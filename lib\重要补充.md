lib\nrc_host.pyd和lib\nrc_interface.py是一个由C++代码通过SWIG工具转换来的Python库，C++接口说明文档地址：https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h.html，https://doc.hmilib.inexbot.coision.cn/nrc__io_8h.html, https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h.html, https://doc.hmilib.inexbot.coision.cn/nrc__modbus_8h.html, https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h.html, https://doc.hmilib.inexbot.coision.cn/nrc__track_8h.html. 所以当使用的Python函数源自带有 & 输出参数的C++接口时：
函数的返回值是一个列表，你需要从列表的第二个元素 ([1]) 中获取你想要的数据。
如果你要循环调用这个函数，请确保在每次调用前，都将传入的那个参数重置为一个初始的整数值。

这是SDK的wiki,包含基本说明和示例:https://ones.inexbot.com/wiki/external/org/8cdyvHV7/#/page/VdKwzFph/KAKi6ovK

